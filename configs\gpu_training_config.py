#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU训练配置文件
针对不同GPU配置的优化参数

作者: AI Assistant
日期: 2024-12-19
"""

import torch

def get_gpu_config():
    """获取GPU优化配置"""
    
    # 检测GPU信息
    if not torch.cuda.is_available():
        return None
    
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
    
    print(f"🚀 检测到GPU: {gpu_name}")
    print(f"💾 GPU内存: {gpu_memory:.1f}GB")
    
    # 根据GPU内存选择配置
    if gpu_memory >= 8:
        # 高端GPU配置 (RTX 3070/4060及以上)
        config = {
            'data': 'configs/dataset.yaml',
            'epochs': 150,             # 更多训练轮数
            'imgsz': 640,              # 标准图像尺寸
            'batch': 32,               # 大批次
            'lr0': 0.01,               # 初始学习率
            'device': 'cuda',          # 使用GPU
            'workers': 8,              # 多线程数据加载
            'project': 'results',      # 结果保存目录
            'name': 'human_detection_gpu_high', # 高端GPU实验名称
            'patience': 50,            # 大耐心值
            'save_period': 10,         # 保存周期
            'verbose': True,           # 详细输出
            'amp': True,               # 混合精度训练
            'cache': True,             # 缓存图像
            'single_cls': True,        # 单类别训练
            'optimizer': 'AdamW',      # AdamW优化器
            'seed': 42,                # 随机种子
            'deterministic': True,     # 确定性训练
            'cos_lr': True,            # 余弦学习率
            'warmup_epochs': 5,        # 预热轮数
            'warmup_momentum': 0.8,    # 预热动量
            'weight_decay': 0.0005,    # 权重衰减
            'momentum': 0.937,         # 动量
            'box': 7.5,                # 边界框损失权重
            'cls': 0.5,                # 分类损失权重
            'dfl': 1.5,                # DFL损失权重
            'hsv_h': 0.015,            # HSV色调增强
            'hsv_s': 0.7,              # HSV饱和度增强
            'hsv_v': 0.4,              # HSV明度增强
            'degrees': 0.0,            # 旋转角度
            'translate': 0.1,          # 平移
            'scale': 0.5,              # 缩放
            'shear': 0.0,              # 剪切
            'perspective': 0.0,        # 透视变换
            'flipud': 0.0,             # 上下翻转
            'fliplr': 0.5,             # 左右翻转
            'mosaic': 1.0,             # 马赛克增强
            'mixup': 0.1,              # 混合增强
            'copy_paste': 0.1,         # 复制粘贴增强
        }
        print("📊 使用高端GPU配置 (8GB+)")
        
    elif gpu_memory >= 4:
        # 中端GPU配置 (GTX 1660/RTX 2060等)
        config = {
            'data': 'configs/dataset.yaml',
            'epochs': 100,             # 适中训练轮数
            'imgsz': 640,              # 标准图像尺寸
            'batch': 16,               # 中等批次
            'lr0': 0.01,               # 初始学习率
            'device': 'cuda',          # 使用GPU
            'workers': 6,              # 中等线程数
            'project': 'results',      # 结果保存目录
            'name': 'human_detection_gpu_mid', # 中端GPU实验名称
            'patience': 30,            # 中等耐心值
            'save_period': 10,         # 保存周期
            'verbose': True,           # 详细输出
            'amp': True,               # 混合精度训练
            'cache': True,             # 缓存图像
            'single_cls': True,        # 单类别训练
            'optimizer': 'AdamW',      # AdamW优化器
            'seed': 42,                # 随机种子
            'deterministic': True,     # 确定性训练
            'cos_lr': True,            # 余弦学习率
            'warmup_epochs': 3,        # 预热轮数
            'warmup_momentum': 0.8,    # 预热动量
            'weight_decay': 0.0005,    # 权重衰减
            'momentum': 0.937,         # 动量
            'box': 7.5,                # 边界框损失权重
            'cls': 0.5,                # 分类损失权重
            'dfl': 1.5,                # DFL损失权重
            'mosaic': 1.0,             # 马赛克增强
            'mixup': 0.05,             # 轻度混合增强
        }
        print("📊 使用中端GPU配置 (4-8GB)")
        
    else:
        # 低端GPU配置 (GTX 1050/1060等)
        config = {
            'data': 'configs/dataset.yaml',
            'epochs': 80,              # 较少训练轮数
            'imgsz': 416,              # 较小图像尺寸
            'batch': 8,                # 小批次
            'lr0': 0.01,               # 初始学习率
            'device': 'cuda',          # 使用GPU
            'workers': 4,              # 较少线程数
            'project': 'results',      # 结果保存目录
            'name': 'human_detection_gpu_low', # 低端GPU实验名称
            'patience': 20,            # 较小耐心值
            'save_period': 10,         # 保存周期
            'verbose': True,           # 详细输出
            'amp': True,               # 混合精度训练
            'cache': False,            # 不缓存图像
            'single_cls': True,        # 单类别训练
            'optimizer': 'SGD',        # SGD优化器
            'seed': 42,                # 随机种子
            'deterministic': True,     # 确定性训练
            'warmup_epochs': 2,        # 少量预热
            'weight_decay': 0.0005,    # 权重衰减
            'momentum': 0.937,         # 动量
        }
        print("📊 使用低端GPU配置 (<4GB)")
    
    return config

def get_cpu_config():
    """获取CPU优化配置"""
    config = {
        'data': 'configs/dataset.yaml',
        'epochs': 50,              # 适中的训练轮数
        'imgsz': 416,              # 较小的图像尺寸，适合CPU
        'batch': 4,                # 小批次大小
        'lr0': 0.01,               # 初始学习率
        'device': 'cpu',           # 使用CPU
        'workers': 2,              # 数据加载线程数
        'project': 'results',      # 结果保存目录
        'name': 'human_detection_cpu', # CPU实验名称
        'patience': 20,            # 早停耐心值
        'save_period': 10,         # 每10轮保存一次
        'verbose': True,           # 详细输出
        'amp': False,              # CPU不支持AMP
        'cache': False,            # 不缓存图像（节省内存）
        'single_cls': True,        # 单类别训练
        'optimizer': 'SGD',        # 优化器
        'seed': 42,                # 随机种子
        'deterministic': True,     # 确定性训练
    }
    print("💻 使用CPU优化配置")
    return config

def get_optimal_config():
    """获取最优配置"""
    gpu_config = get_gpu_config()
    if gpu_config:
        return gpu_config
    else:
        return get_cpu_config()

def print_config_summary(config):
    """打印配置摘要"""
    print("\n📋 训练配置摘要:")
    print(f"   设备: {config['device']}")
    print(f"   训练轮数: {config['epochs']}")
    print(f"   图像尺寸: {config['imgsz']}")
    print(f"   批次大小: {config['batch']}")
    print(f"   学习率: {config['lr0']}")
    print(f"   优化器: {config['optimizer']}")
    print(f"   混合精度: {config['amp']}")
    print(f"   数据缓存: {config['cache']}")
    print(f"   工作线程: {config['workers']}")
    
    # 估算训练时间
    if config['device'] == 'cuda':
        if config['batch'] >= 32:
            time_estimate = "约2-4小时"
        elif config['batch'] >= 16:
            time_estimate = "约4-8小时"
        else:
            time_estimate = "约6-12小时"
    else:
        time_estimate = "约8-15小时"
    
    print(f"   预计训练时间: {time_estimate}")

if __name__ == "__main__":
    # 测试配置
    config = get_optimal_config()
    print_config_summary(config)
