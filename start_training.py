#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速开始训练脚本
一键启动人体检测模型训练

使用方法:
    python start_training.py

作者: AI Assistant
日期: 2024-12-19
"""

import os
import yaml
import time
import json
from pathlib import Path

def check_environment():
    """检查训练环境"""
    print("🔍 检查训练环境...")
    
    # 检查必要的包
    try:
        import torch  # noqa: F401
        import cv2  # noqa: F401
        import numpy as np  # noqa: F401
        from ultralytics import YOLO  # noqa: F401
        print("✅ 所有必要的包都已安装")
    except ImportError as e:
        print(f"❌ 缺少必要的包: {e}")
        print("请运行: pip install ultralytics opencv-python torch torchvision")
        return False
    
    # 检查数据集
    dataset_path = Path("wuxi_video_2")
    if not dataset_path.exists():
        print(f"❌ 数据集目录不存在: {dataset_path}")
        return False
    
    image_dir = dataset_path / "image"
    labels_dir = dataset_path / "labels"
    
    if not image_dir.exists():
        print(f"❌ 图像目录不存在: {image_dir}")
        return False
    
    if not labels_dir.exists():
        print(f"❌ 标注目录不存在: {labels_dir}")
        return False
    
    # 统计数据
    images = list(image_dir.glob("*.jpg"))
    train_labels = list((labels_dir / "train").glob("*.txt")) if (labels_dir / "train").exists() else []
    val_labels = list((labels_dir / "val").glob("*.txt")) if (labels_dir / "val").exists() else []
    
    print(f"📊 数据集统计:")
    print(f"   图像总数: {len(images)}")
    print(f"   训练标注: {len(train_labels)}")
    print(f"   验证标注: {len(val_labels)}")
    
    if len(images) == 0:
        print("❌ 没有找到图像文件")
        return False
    
    if len(train_labels) == 0:
        print("❌ 没有找到训练标注文件")
        return False
    
    print("✅ 数据集检查通过")
    return True

def create_dataset_config():
    """创建数据集配置文件"""
    print("📝 创建数据集配置文件...")
    
    # 创建配置目录
    config_dir = Path("configs")
    config_dir.mkdir(exist_ok=True)
    
    # 数据集配置
    dataset_config = {
        'path': str(Path("wuxi_video_2").absolute()),
        'train': 'image',
        'val': 'image',
        'nc': 1,
        'names': ['person']
    }
    
    # 保存配置文件
    config_file = config_dir / "dataset.yaml"
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ 配置文件已创建: {config_file}")
    return config_file

def create_training_directories():
    """创建训练相关目录"""
    print("📁 创建训练目录...")
    
    directories = [
        "results",
        "models",
        "logs"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"   ✅ {dir_name}/")

def start_training():
    """开始训练"""
    print("\n🚀 开始训练人体检测模型...")
    
    try:
        from ultralytics import YOLO
        
        # 使用智能配置系统
        try:
            from configs.gpu_training_config import get_optimal_config, print_config_summary
            config = get_optimal_config()
            print_config_summary(config)
        except ImportError:
            # 如果配置文件不存在，使用默认配置
            import torch
            if torch.cuda.is_available():
                print("🚀 检测到GPU，使用GPU配置")
                config = {
                    'data': 'configs/dataset.yaml',
                    'epochs': 100,
                    'imgsz': 640,
                    'batch': 16,
                    'device': 'cuda',
                    'workers': 8,
                    'name': 'human_detection_gpu',
                    'amp': True,
                    'cache': True,
                    'optimizer': 'AdamW',
                }
            else:
                print("💻 使用CPU配置")
                config = {
                    'data': 'configs/dataset.yaml',
                    'epochs': 50,
                    'imgsz': 416,
                    'batch': 4,
                    'device': 'cpu',
                    'workers': 2,
                    'name': 'human_detection_cpu',
                    'amp': False,
                    'cache': False,
                    'optimizer': 'SGD',
                }
        
        print("📋 训练配置:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        # 初始化模型
        print("\n🔧 初始化YOLOv8模型...")
        model = YOLO('yolov8n.pt')  # 使用最小的nano版本
        
        # 开始训练
        print("\n⏰ 开始训练... (这可能需要一些时间)")
        print("💡 提示: 您可以按 Ctrl+C 来停止训练")
        
        start_time = time.time()
        
        results = model.train(**config)
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n🎉 训练完成!")
        print(f"⏱️  总训练时间: {training_time/3600:.2f} 小时")
        print(f"📁 结果保存在: {results.save_dir}")
        print(f"🏆 最佳模型: {results.save_dir}/weights/best.pt")
        
        # 保存训练信息
        training_info = {
            'training_time': training_time,
            'config': config,
            'save_dir': str(results.save_dir),
            'best_model': str(results.save_dir / 'weights' / 'best.pt'),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open('training_info.json', 'w', encoding='utf-8') as f:
            json.dump(training_info, f, indent=2, ensure_ascii=False)
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        return False

def test_trained_model():
    """测试训练好的模型"""
    print("\n🧪 测试训练好的模型...")
    
    # 查找最新的训练结果
    results_dir = Path("results")
    if not results_dir.exists():
        print("❌ 没有找到训练结果")
        return
    
    # 查找最佳模型
    model_paths = list(results_dir.glob("*/weights/best.pt"))
    if not model_paths:
        print("❌ 没有找到训练好的模型")
        return
    
    # 使用最新的模型
    model_path = sorted(model_paths, key=lambda x: x.stat().st_mtime)[-1]
    print(f"🔍 使用模型: {model_path}")
    
    try:
        from ultralytics import YOLO
        import cv2  # noqa: F401
        
        # 加载模型
        model = YOLO(str(model_path))
        
        # 测试图像
        test_images = [
            "wuxi_video_2/image/wuxi_2_0000.jpg",
            "wuxi_video_2/image/wuxi_2_0100.jpg",
            "wuxi_video_2/image/wuxi_2_0200.jpg"
        ]
        
        total_time = 0
        total_detections = 0
        
        for img_path in test_images:
            if os.path.exists(img_path):
                print(f"\n📸 测试图像: {img_path}")
                
                # 推理
                start_time = time.time()
                results = model.predict(img_path, verbose=False)
                inference_time = time.time() - start_time
                total_time += inference_time
                
                # 分析结果
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        num_detections = len(boxes)
                        total_detections += num_detections
                        
                        print(f"   🎯 检测到 {num_detections} 个人体目标")
                        print(f"   ⏱️  推理时间: {inference_time*1000:.2f}ms")
                        
                        # 显示检测详情
                        for i, box in enumerate(boxes):
                            conf = box.conf.item()
                            x1, y1, x2, y2 = box.xyxy[0].tolist()  # noqa: F841
                            print(f"      目标{i+1}: 置信度={conf:.3f}")
                    else:
                        print("   ❌ 未检测到目标")
        
        # 性能统计
        if len(test_images) > 0:
            avg_time = total_time / len(test_images)
            fps = 1 / avg_time
            
            print(f"\n📊 性能统计:")
            print(f"   平均推理时间: {avg_time*1000:.2f}ms")
            print(f"   平均FPS: {fps:.1f}")
            print(f"   总检测目标数: {total_detections}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 浓烟环境人体目标检测 - 模型训练")
    print("=" * 60)
    
    # 步骤1: 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 步骤2: 创建配置文件
    create_dataset_config()
    
    # 步骤3: 创建目录
    create_training_directories()
    
    # 步骤4: 开始训练
    success = start_training()
    
    if success:
        # 步骤5: 测试模型
        test_trained_model()
        
        print("\n" + "=" * 60)
        print("🎉 训练流程完成!")
        print("📖 查看详细使用说明: docs/模型训练指南.md")
        print("🚀 下一步: 运行 python src/integrated_system.py 测试完整系统")
        print("=" * 60)
    else:
        print("\n❌ 训练未成功完成")

if __name__ == "__main__":
    main()
